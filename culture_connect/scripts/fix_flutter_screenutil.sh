#!/bin/bash

# <PERSON>ript to systematically fix flutter_screenutil usage across the codebase
# This script will replace flutter_screenutil patterns with standard Flutter approaches

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting flutter_screenutil removal process...${NC}"

# Navigate to the lib directory
cd "$(dirname "$0")/../lib" || exit 1

# Function to process a single file
process_file() {
    local file="$1"
    echo -e "${YELLOW}Processing: $file${NC}"
    
    # Remove flutter_screenutil import
    sed -i '' '/import.*flutter_screenutil/d' "$file"
    
    # Replace common patterns
    # .sp -> fixed font sizes
    sed -i '' 's/fontSize: \([0-9]\+\)\.sp/fontSize: \1/g' "$file"
    sed -i '' 's/size: \([0-9]\+\)\.sp/size: \1/g' "$file"

    # .w and .h -> fixed values for simple cases
    sed -i '' 's/width: \([0-9]\+\)\.w/width: \1/g' "$file"
    sed -i '' 's/height: \([0-9]\+\)\.h/height: \1/g' "$file"
    sed -i '' 's/SizedBox(width: \([0-9]\+\)\.w)/const SizedBox(width: \1)/g' "$file"
    sed -i '' 's/SizedBox(height: \([0-9]\+\)\.h)/const SizedBox(height: \1)/g' "$file"

    # .r -> fixed radius values
    sed -i '' 's/BorderRadius\.circular(\([0-9]\+\)\.r)/BorderRadius.circular(\1)/g' "$file"
    sed -i '' 's/borderRadius: BorderRadius\.circular(\([0-9]\+\)\.r)/borderRadius: BorderRadius.circular(\1)/g' "$file"

    # EdgeInsets patterns
    sed -i '' 's/EdgeInsets\.all(\([0-9]\+\)\.r)/const EdgeInsets.all(\1)/g' "$file"
    sed -i '' 's/EdgeInsets\.all(\([0-9]\+\)\.w)/const EdgeInsets.all(\1)/g' "$file"
    sed -i '' 's/EdgeInsets\.all(\([0-9]\+\)\.h)/const EdgeInsets.all(\1)/g' "$file"

    # EdgeInsets.symmetric patterns
    sed -i '' 's/EdgeInsets\.symmetric(vertical: \([0-9]\+\)\.h, horizontal: \([0-9]\+\)\.w)/const EdgeInsets.symmetric(vertical: \1, horizontal: \2)/g' "$file"
    sed -i '' 's/EdgeInsets\.symmetric(horizontal: \([0-9]\+\)\.w, vertical: \([0-9]\+\)\.h)/const EdgeInsets.symmetric(horizontal: \1, vertical: \2)/g' "$file"

    # EdgeInsets.only patterns
    sed -i '' 's/EdgeInsets\.only(bottom: \([0-9]\+\)\.h)/const EdgeInsets.only(bottom: \1)/g' "$file"
    sed -i '' 's/EdgeInsets\.only(top: \([0-9]\+\)\.h)/const EdgeInsets.only(top: \1)/g' "$file"
    sed -i '' 's/EdgeInsets\.only(left: \([0-9]\+\)\.w)/const EdgeInsets.only(left: \1)/g' "$file"
    sed -i '' 's/EdgeInsets\.only(right: \([0-9]\+\)\.w)/const EdgeInsets.only(right: \1)/g' "$file"

    # More complex patterns that were missed
    sed -i '' 's/\([0-9]\+\)\.sp/\1/g' "$file"
    sed -i '' 's/\([0-9]\+\)\.w/\1/g' "$file"
    sed -i '' 's/\([0-9]\+\)\.h/\1/g' "$file"
    sed -i '' 's/\([0-9]\+\)\.r/\1/g' "$file"

    # Fix padding patterns
    sed -i '' 's/padding: EdgeInsets\./padding: const EdgeInsets./g' "$file"
    sed -i '' 's/margin: EdgeInsets\./margin: const EdgeInsets./g' "$file"
    
    # withOpacity to withAlpha conversions
    sed -i '' 's/\.withOpacity(0\.1)/.withAlpha(26)/g' "$file"
    sed -i '' 's/\.withOpacity(0\.2)/.withAlpha(51)/g' "$file"
    sed -i '' 's/\.withOpacity(0\.3)/.withAlpha(77)/g' "$file"
    sed -i '' 's/\.withOpacity(0\.5)/.withAlpha(128)/g' "$file"
    sed -i '' 's/\.withOpacity(0\.8)/.withAlpha(204)/g' "$file"
    sed -i '' 's/\.withOpacity(0\.9)/.withAlpha(230)/g' "$file"
    
    echo -e "${GREEN}Completed: $file${NC}"
}

# Find all Dart files and process them
echo -e "${YELLOW}Finding Dart files with flutter_screenutil usage...${NC}"

# Process files in batches to avoid overwhelming the system
find . -name "*.dart" -type f | while read -r file; do
    if grep -q "flutter_screenutil\|\.sp\|\.w\|\.h\|\.r\|withOpacity" "$file"; then
        process_file "$file"
    fi
done

echo -e "${GREEN}Flutter screenutil removal process completed!${NC}"
echo -e "${YELLOW}Note: Some complex patterns may need manual review.${NC}"
echo -e "${YELLOW}Run 'flutter analyze' to check for remaining issues.${NC}"
