// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/database/translation_database_helper.dart';
import 'package:culture_connect/services/translation_sync_service.dart';

/// A dialog for resolving translation conflicts
class TranslationConflictDialog extends ConsumerWidget {
  /// The message ID
  final String messageId;

  /// The local version of the translation
  final String localVersion;

  /// The server version of the translation
  final String serverVersion;

  /// Creates a new translation conflict dialog
  const TranslationConflictDialog({
    super.key,
    required this.messageId,
    required this.localVersion,
    required this.serverVersion,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog(
      title: const Text('Translation Conflict'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'There is a conflict between your local translation and the server version.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            _buildVersionComparison(context),
            const SizedBox(height: 16),
            const Text(
              'How would you like to resolve this conflict?',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop('cancel'),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _resolveConflict(context, ref, 'local'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.teal, // Secondary color
          ),
          child: const Text('Keep Local'),
        ),
        ElevatedButton(
          onPressed: () => _resolveConflict(context, ref, 'server'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue, // Primary color
          ),
          child: const Text('Use Server'),
        ),
      ],
    );
  }

  /// Builds the version comparison widget
  Widget _buildVersionComparison(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildVersionHeader('Local Version', Colors.teal), // Secondary color
          Divider(height: 1, color: Colors.grey.shade300),
          _buildVersionContent(localVersion),
          Divider(height: 1, color: Colors.grey.shade300),
          _buildVersionHeader('Server Version', Colors.blue), // Primary color
          Divider(height: 1, color: Colors.grey.shade300),
          _buildVersionContent(serverVersion),
        ],
      ),
    );
  }

  /// Builds a version header
  Widget _buildVersionHeader(String title, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: color.withAlpha(25),
      child: Row(
        children: [
          Icon(Icons.translate, size: 16, color: color),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a version content
  Widget _buildVersionContent(String content) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Text(
        content,
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  /// Resolves the conflict
  Future<void> _resolveConflict(
      BuildContext context, WidgetRef ref, String resolution) async {
    final syncService = ref.read(translationSyncServiceProvider);
    await syncService.resolveConflict(messageId, resolution);
    if (context.mounted) {
      Navigator.of(context).pop(resolution);
    }
  }
}

/// A widget that displays a list of translation conflicts
class TranslationConflictsList extends ConsumerWidget {
  /// Creates a new translation conflicts list
  const TranslationConflictsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conflictsAsync = ref.watch(translationConflictsProvider);

    return conflictsAsync.when(
      data: (conflicts) {
        if (conflicts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 48,
                  color: Colors.green, // Success color
                ),
                SizedBox(height: 16),
                Text(
                  'No translation conflicts',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'All translations are in sync',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          );
        }

        return ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: conflicts.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final conflict = conflicts[index];
            return _buildConflictItem(context, ref, conflict);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading conflicts: $error'),
      ),
    );
  }

  /// Builds a conflict item
  Widget _buildConflictItem(
      BuildContext context, WidgetRef ref, Map<String, dynamic> conflict) {
    final messageId = conflict['message_id'] as String;
    final localVersion = conflict['local_version'] as String;
    final serverVersion = conflict['server_version'] as String;

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _showConflictDialog(
            context, ref, messageId, localVersion, serverVersion),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Translation Conflict',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Message ID: $messageId',
                style: const TextStyle(fontSize: 12),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildVersionPreview(
                        'Local', localVersion, Colors.teal), // Secondary color
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildVersionPreview(
                        'Server', serverVersion, Colors.blue), // Primary color
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: () => _showConflictDialog(
                      context, ref, messageId, localVersion, serverVersion),
                  icon: const Icon(Icons.compare_arrows, size: 16),
                  label: const Text('Resolve Conflict'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a version preview
  Widget _buildVersionPreview(String label, String content, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(fontSize: 12),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Shows the conflict resolution dialog
  Future<void> _showConflictDialog(
    BuildContext context,
    WidgetRef ref,
    String messageId,
    String localVersion,
    String serverVersion,
  ) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => TranslationConflictDialog(
        messageId: messageId,
        localVersion: localVersion,
        serverVersion: serverVersion,
      ),
    );

    if (result != null && result != 'cancel') {
      // Refresh the conflicts list
      ref.invalidate(translationConflictsProvider);
    }
  }
}

/// Provider for translation conflicts
final translationConflictsProvider =
    FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final dbHelper = TranslationDatabaseHelper();
  return await dbHelper.getUnresolvedConflicts();
});
