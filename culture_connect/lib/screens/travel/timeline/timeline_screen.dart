import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';
import 'package:culture_connect/screens/travel/timeline/timeline_event_details_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/offline/offline_banner.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_timeline_view.dart';

/// A screen for displaying a timeline with AR integration
class TimelineScreen extends ConsumerStatefulWidget {
  /// The timeline ID
  final String timelineId;

  /// Creates a new timeline screen
  const TimelineScreen({
    super.key,
    required this.timelineId,
  });

  @override
  ConsumerState<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends ConsumerState<TimelineScreen>
    with SingleTickerProviderStateMixin {
  /// Animation controller for AR content animations
  late AnimationController _animationController;

  /// Whether AR content is being previewed
  bool _isPreviewingAR = false;

  /// The currently selected event for AR preview
  TimelineEvent? _selectedEvent;

  /// Logging service
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Load the timeline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(currentTimelineProvider.notifier)
          .loadTimeline(widget.timelineId);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar(Timeline timeline) {
    return CustomAppBar(
      title: timeline.title,
      showBackButton: true,
      actions: [
        // AR content filter button
        IconButton(
          icon: const Icon(Icons.view_in_ar),
          tooltip: 'Show AR Content Only',
          onPressed: () {
            // Toggle AR content filter
            ref.read(timelineARFilterProvider.notifier).state =
                !ref.read(timelineARFilterProvider);
          },
        ),

        // Share button
        IconButton(
          icon: const Icon(Icons.share),
          tooltip: 'Share Timeline',
          onPressed: () {
            // Share timeline
          },
        ),

        // More options
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                // Navigate to edit screen
                break;
              case 'delete':
                _showDeleteTimelineConfirmation(timeline);
                break;
              case 'export':
                // Export timeline
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Text('Edit Timeline'),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Text('Export Timeline'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child:
                  Text('Delete Timeline', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      ],
    );
  }

  /// Show delete timeline confirmation dialog
  void _showDeleteTimelineConfirmation(Timeline timeline) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Timeline'),
          content: Text('Are you sure you want to delete "${timeline.title}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(currentTimelineProvider.notifier).deleteTimeline();
                Navigator.of(context).pop(); // Go back to previous screen
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  /// Handle event tap
  void _handleEventTap(TimelineEvent event) {
    try {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TimelineEventDetailsScreen(
            event: event,
          ),
        ),
      );
    } catch (e, stackTrace) {
      // Log the error
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error navigating to event details',
        e,
        stackTrace,
      );

      // Show a snackbar to the user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to open event details. Please try again.'),
        ),
      );
    }
  }

  /// Handle event long press
  void _handleEventLongPress(TimelineEvent event) {
    // Show options dialog
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'Event Options',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const Divider(),

              // View details
              ListTile(
                leading: const Icon(Icons.info_outline),
                title: const Text('View Details'),
                onTap: () {
                  Navigator.of(context).pop();
                  _handleEventTap(event);
                },
              ),

              // Edit event
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Event'),
                onTap: () {
                  Navigator.of(context).pop();
                  // Navigate to edit screen
                },
              ),

              // Delete event
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Event',
                    style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.of(context).pop();
                  _showDeleteConfirmation(event);
                },
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  /// Handle event drag end
  void _handleEventDragEnd(TimelineEvent event, DateTime newDate) {
    try {
      // Update the event with the new date
      final updatedEvent = event.copyWith(
        eventDate: newDate,
      );

      // Update the event in the timeline
      ref.read(currentTimelineProvider.notifier).updateEvent(updatedEvent);
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error updating event date',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update event date. Please try again.'),
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(TimelineEvent event) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Event'),
          content: Text('Are you sure you want to delete "${event.title}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref
                    .read(currentTimelineProvider.notifier)
                    .removeEvent(event.id);
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  /// Handle AR content tap
  void _handleARContentTap(TimelineEvent event) {
    if (event.arContentId == null) return;

    try {
      if (!mounted) return;
      setState(() {
        _isPreviewingAR = true;
        _selectedEvent = event;
      });

      // Load AR content
      ref
          .read(currentARContentMarkerProvider.notifier)
          .loadARContentMarker(event.arContentId!);

      // Start animation
      _animationController.forward();
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error handling AR content tap',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load AR content. Please try again.'),
          ),
        );
      }
    }
  }

  /// Handle AR preview close
  void _handleARPreviewClose() {
    try {
      // Reverse animation and then hide preview
      _animationController.reverse().then((_) {
        if (!mounted) return;
        setState(() {
          _isPreviewingAR = false;
          _selectedEvent = null;
        });
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error closing AR preview',
        e,
        stackTrace,
      );
    }
  }

  /// Navigate to full AR preview
  void _navigateToFullARPreview(String arContentId) {
    try {
      // Close the preview overlay first
      _handleARPreviewClose();

      // Navigate to full AR preview
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ARContentPreviewScreen(
            arContentId: arContentId,
          ),
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error navigating to full AR preview',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to open AR preview. Please try again.'),
          ),
        );
      }
    }
  }

  /// Build the AR preview overlay
  Widget _buildARPreviewOverlay() {
    if (!_isPreviewingAR || _selectedEvent?.arContentId == null) {
      return const SizedBox.shrink();
    }

    final arContentMarkerAsync = ref.watch(currentARContentMarkerProvider);

    return Positioned.fill(
      child: GestureDetector(
        onTap: _handleARPreviewClose,
        child: Container(
          color: Colors.black.withAlpha(179),
          child: Center(
            child: arContentMarkerAsync.when(
              data: (marker) {
                if (marker == null) {
                  return const Text(
                    'AR content not found',
                    style: TextStyle(color: Colors.white),
                  );
                }

                return _buildARPreviewContent(marker);
              },
              loading: () => const LoadingIndicator(),
              error: (error, stackTrace) => ErrorView(
                error: error.toString(),
                onRetry: () {
                  if (_selectedEvent?.arContentId != null) {
                    ref
                        .read(currentARContentMarkerProvider.notifier)
                        .loadARContentMarker(_selectedEvent!.arContentId!);
                  }
                },
              ),
            ),
          ),
        ),
      ),
    ).animate(controller: _animationController).fadeIn();
  }

  /// Build the AR preview content
  Widget _buildARPreviewContent(ARContentMarker marker) {
    return Container(
      width: 300,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              // AR type icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: marker.contentType.color.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  marker.contentType.icon,
                  color: marker.contentType.color,
                  size: 20,
                ),
              ),

              SizedBox(width: 12),

              // Title
              Expanded(
                child: Text(
                  marker.title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Close button
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: _handleARPreviewClose,
              ),
            ],
          ),

          SizedBox(height: 16),

          // Preview image or placeholder
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              image: marker.thumbnailUrl != null
                  ? DecorationImage(
                      image: NetworkImage(marker.thumbnailUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: marker.thumbnailUrl == null
                ? Center(
                    child: Icon(
                      marker.contentType.icon,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                  )
                : null,
          ),

          SizedBox(height: 16),

          // Description
          if (marker.description != null) ...[
            Text(
              marker.description!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 16),
          ],

          // Actions
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // View in AR button
              ElevatedButton.icon(
                onPressed: () {
                  if (_selectedEvent?.arContentId != null) {
                    _navigateToFullARPreview(_selectedEvent!.arContentId!);
                  }
                },
                icon: const Icon(Icons.view_in_ar),
                label: const Text('View in AR'),
              ),
            ],
          ),
        ],
      ),
    ).animate().scale(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutBack,
        );
  }

  @override
  Widget build(BuildContext context) {
    final timelineAsync = ref.watch(currentTimelineProvider);
    final showARContentOnly = ref.watch(timelineARFilterProvider);

    return Scaffold(
      body: timelineAsync.when(
        data: (timeline) {
          if (timeline == null) {
            return const Center(
              child: Text('Timeline not found'),
            );
          }

          return Stack(
            children: [
              Scaffold(
                appBar: _buildAppBar(timeline),
                body: OfflineBannerWrapper(
                  message: 'You are offline. Some features may be limited.',
                  actionText: 'CONTINUE',
                  child: ARTimelineView(
                    timeline: timeline,
                    showARContentOnly: showARContentOnly,
                    onEventTap: _handleEventTap,
                    onARContentTap: _handleARContentTap,
                  ),
                ),
                floatingActionButton: FloatingActionButton(
                  onPressed: () {
                    // Navigate to add event screen
                  },
                  child: const Icon(Icons.add),
                ),
              ),

              // AR preview overlay
              _buildARPreviewOverlay(),
            ],
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () {
              ref
                  .read(currentTimelineProvider.notifier)
                  .loadTimeline(widget.timelineId);
            },
          ),
        ),
      ),
    );
  }
}
