import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/language_pack_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/providers/offline_settings_provider.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/offline_translation_manager.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/providers/bandwidth_provider.dart';

/// A screen for managing language packs
class LanguagePackManagerScreen extends ConsumerStatefulWidget {
  /// Creates a new language pack manager screen
  const LanguagePackManagerScreen({super.key});

  @override
  ConsumerState<LanguagePackManagerScreen> createState() =>
      _LanguagePackManagerScreenState();
}

class _LanguagePackManagerScreenState
    extends ConsumerState<LanguagePackManagerScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showStorageWarning = false;
  bool _showBandwidthWarning = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _checkStorageAndBandwidth();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Check storage and bandwidth status
  Future<void> _checkStorageAndBandwidth() async {
    final offlineTranslationManager =
        ref.read(offlineTranslationManagerProvider);
    final totalSize =
        await offlineTranslationManager.getTotalLanguagePackSize();
    final offlineSettings = ref.read(offlineSettingsProvider);

    // Check if we're approaching storage limit
    if (totalSize > offlineSettings.maxStorageSpace * 0.8) {
      setState(() {
        _showStorageWarning = true;
      });
    }

    // Check if we're on a metered connection and have bandwidth limits
    final isMeteredConnection = await _isMeteredConnection();
    final bandwidthUsage = ref.read(bandwidthUsageProvider).value;

    if (isMeteredConnection &&
        offlineSettings.maxDailyBandwidthUsage > 0 &&
        bandwidthUsage != null &&
        bandwidthUsage.downloadMB >
            offlineSettings.maxDailyBandwidthUsage * 0.8) {
      setState(() {
        _showBandwidthWarning = true;
      });
    }
  }

  /// Check if we're on a metered connection
  Future<bool> _isMeteredConnection() async {
    // In a real app, this would check the connection type
    // For now, we'll just return false
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final languagePacksAsync = ref.watch(languagePacksProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Language Packs',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Warning banners
          if (_showStorageWarning)
            Container(
              padding: const EdgeInsets.all(8),
              color: Colors.orange.withAlpha(51),
              child: Row(
                children: [
                  const Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Storage space is limited. Consider removing unused language packs.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[800],
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.orange[800],
                    ),
                    onPressed: () {
                      setState(() {
                        _showStorageWarning = false;
                      });
                    },
                  ),
                ],
              ),
            ),

          if (_showBandwidthWarning)
            Container(
              padding: const EdgeInsets.all(8),
              color: Colors.red.withAlpha(51),
              child: Row(
                children: [
                  const Icon(
                    Icons.signal_cellular_connected_no_internet_4_bar,
                    color: Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You\'re approaching your daily bandwidth limit. Downloads may be paused.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red[800],
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.red[800],
                    ),
                    onPressed: () {
                      setState(() {
                        _showBandwidthWarning = false;
                      });
                    },
                  ),
                ],
              ),
            ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Downloaded'),
              Tab(text: 'Available'),
              Tab(text: 'Settings'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Downloaded languages
                languagePacksAsync.when(
                  data: (languagePacks) =>
                      _buildDownloadedLanguagesList(languagePacks),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stackTrace) => Center(
                    child: Text('Error: $error'),
                  ),
                ),

                // Available languages
                _buildAvailableLanguagesList(),

                // Settings
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadedLanguagesList(List<LanguagePackModel> languagePacks) {
    final downloadedPacks = languagePacks
        .where((pack) =>
            pack.status == LanguagePackStatus.downloaded ||
            pack.status == LanguagePackStatus.updateAvailable)
        .toList();

    if (downloadedPacks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.language,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No language packs downloaded',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Download language packs to use offline translation',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                _tabController.animateTo(1);
              },
              icon: const Icon(Icons.download),
              label: const Text('Download Languages'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: downloadedPacks.length,
      itemBuilder: (context, index) {
        final pack = downloadedPacks[index];
        return _buildLanguagePackItem(pack);
      },
    );
  }

  Widget _buildAvailableLanguagesList() {
    return Consumer(
      builder: (context, ref, child) {
        final languagePacks = ref.watch(languagePacksProvider).value ?? [];
        final downloadedLanguageCodes = languagePacks
            .where((pack) =>
                pack.status == LanguagePackStatus.downloaded ||
                pack.status == LanguagePackStatus.downloading)
            .map((pack) => pack.languageCode)
            .toSet();

        final availableLanguages = supportedLanguages
            .where((lang) =>
                lang.isOfflineAvailable &&
                !downloadedLanguageCodes.contains(lang.code))
            .toList();

        if (availableLanguages.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.check_circle,
                  size: 64,
                  color: Colors.green,
                ),
                const SizedBox(height: 16),
                Text(
                  'All available language packs downloaded',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You can now use offline translation for all supported languages',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.symmetric(vertical: 8),
          itemCount: availableLanguages.length,
          itemBuilder: (context, index) {
            final language = availableLanguages[index];
            return _buildAvailableLanguageItem(language);
          },
        );
      },
    );
  }

  Widget _buildLanguagePackItem(LanguagePackModel pack) {
    final isPrimary = pack.isPrimary;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Language info
            Row(
              children: [
                // Language flag and name
                Text(
                  pack.languageFlag,
                  style: TextStyle(
                    fontSize: 24,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pack.languageName,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Version ${pack.version} • ${pack.sizeMB.toStringAsFixed(1)} MB',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),

                // Primary language badge
                if (isPrimary)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(26),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppTheme.primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Primary',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(height: 16),

            // Dialect selection
            if (pack.status == LanguagePackStatus.downloaded) ...[
              Text(
                'Dialect',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8),
              _buildDialectSelector(pack),
              SizedBox(height: 16),
            ],

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Set as primary button
                if (!isPrimary && pack.status == LanguagePackStatus.downloaded)
                  TextButton.icon(
                    onPressed: () => _setPrimaryLanguage(pack.languageCode),
                    icon: Icon(
                      Icons.star,
                      size: 16,
                    ),
                    label: Text(
                      'Set as Primary',
                      style: TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),

                SizedBox(width: 8),

                // Delete button
                TextButton.icon(
                  onPressed: () => _confirmDeleteLanguagePack(pack),
                  icon: Icon(
                    Icons.delete,
                    size: 16,
                    color: Colors.red,
                  ),
                  label: Text(
                    'Delete',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDialectSelector(LanguagePackModel pack) {
    final language = supportedLanguages.firstWhere(
      (lang) => lang.code == pack.languageCode,
      orElse: () => const LanguageModel(
        code: 'unknown',
        name: 'Unknown',
        flag: '🏳️',
      ),
    );

    if (language.supportedDialects.isEmpty) {
      return Text(
        'No dialects available for this language',
        style: TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryColor,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return DropdownButtonFormField<String>(
      value: pack.selectedDialect ?? language.supportedDialects.first,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      items: language.supportedDialects.map((dialect) {
        return DropdownMenuItem<String>(
          value: dialect,
          child: Text(dialect),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          _setSelectedDialect(pack.languageCode, value);
        }
      },
    );
  }

  Widget _buildAvailableLanguageItem(LanguageModel language) {
    final downloadProgressAsync =
        ref.watch(languagePackDownloadProgressProvider(language.code));
    final isDownloading = downloadProgressAsync.value != null &&
        downloadProgressAsync.value! > 0 &&
        downloadProgressAsync.value! < 1;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Language info
            Row(
              children: [
                // Language flag and name
                Text(
                  language.flag,
                  style: TextStyle(
                    fontSize: 24,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language.name,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '${language.downloadSizeMB.toStringAsFixed(1)} MB • ${language.supportedDialects.length} dialects',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Download progress
            if (isDownloading)
              downloadProgressAsync.when(
                data: (progress) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Downloading... ${(progress * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: Colors.grey[200],
                      valueColor: const AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryColor),
                    ),
                  ],
                ),
                loading: () => const LinearProgressIndicator(),
                error: (_, __) => const SizedBox.shrink(),
              ),

            // Download button
            if (!isDownloading)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _downloadLanguagePack(language.code),
                  icon: const Icon(Icons.download),
                  label: Text('Download ${language.name}'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadLanguagePack(String languageCode) async {
    try {
      await ref
          .read(languagePackServiceProvider)
          .downloadLanguagePack(languageCode);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading language pack: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _setPrimaryLanguage(String languageCode) async {
    try {
      await ref
          .read(languagePackServiceProvider)
          .setPrimaryLanguage(languageCode);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Primary language set successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error setting primary language: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _setSelectedDialect(String languageCode, String dialect) async {
    try {
      await ref
          .read(languagePackServiceProvider)
          .setSelectedDialect(languageCode, dialect);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Dialect set successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error setting dialect: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Build the settings tab
  Widget _buildSettingsTab() {
    final offlineSettings = ref.watch(offlineSettingsProvider);

    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        // Storage settings
        _buildSettingsSection(
          title: 'Storage Settings',
          icon: Icons.storage,
          children: [
            // Max storage space
            _buildSliderSetting(
              title: 'Maximum Storage Space',
              subtitle: 'Maximum space to use for language packs',
              value: offlineSettings.maxStorageSpace / (1024 * 1024 * 1024),
              min: 0.1,
              max: 5.0,
              divisions: 49,
              label:
                  '${(offlineSettings.maxStorageSpace / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB',
              onChanged: (value) {
                final bytes = (value * 1024 * 1024 * 1024).round();
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setMaxStorageSpace(bytes);
              },
            ),

            // Auto cleanup expired content
            _buildSwitchSetting(
              title: 'Auto-cleanup Unused Language Packs',
              subtitle:
                  'Automatically remove language packs that haven\'t been used recently',
              value: offlineSettings.autoCleanupExpiredContent,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setAutoCleanupExpiredContent(value);
              },
            ),

            // Auto cleanup when storage low
            _buildSwitchSetting(
              title: 'Auto-cleanup When Storage is Low',
              subtitle:
                  'Automatically remove language packs when storage is running low',
              value: offlineSettings.autoCleanupWhenStorageLow,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setAutoCleanupWhenStorageLow(value);
              },
            ),

            // Storage threshold for cleanup
            _buildSliderSetting(
              title: 'Storage Threshold for Cleanup',
              subtitle: 'When to start cleaning up language packs',
              value: offlineSettings.storageThresholdForCleanup / (1024 * 1024),
              min: 10,
              max: 500,
              divisions: 49,
              label:
                  '${(offlineSettings.storageThresholdForCleanup / (1024 * 1024)).round()} MB',
              onChanged: (value) {
                final bytes = (value * 1024 * 1024).round();
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setStorageThresholdForCleanup(bytes);
              },
            ),

            // Clean up now button
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: ElevatedButton.icon(
                onPressed: _cleanupUnusedLanguagePacks,
                icon: const Icon(Icons.cleaning_services),
                label: const Text('Clean Up Unused Language Packs'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 16),

        // Bandwidth settings
        _buildSettingsSection(
          title: 'Bandwidth Settings',
          icon: Icons.network_check,
          children: [
            // Sync only on WiFi
            _buildSwitchSetting(
              title: 'Sync Only on WiFi',
              subtitle: 'Only download language packs when connected to WiFi',
              value: offlineSettings.syncOnlyOnWifi,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setSyncOnlyOnWifi(value);
              },
            ),

            // Sync only when charging
            _buildSwitchSetting(
              title: 'Sync Only When Charging',
              subtitle: 'Only download language packs when device is charging',
              value: offlineSettings.syncOnlyWhenCharging,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setSyncOnlyWhenCharging(value);
              },
            ),

            // Max daily bandwidth usage
            _buildSliderSetting(
              title: 'Maximum Daily Bandwidth Usage',
              subtitle: 'Maximum bandwidth to use per day for language packs',
              value: offlineSettings.maxDailyBandwidthUsage > 0
                  ? offlineSettings.maxDailyBandwidthUsage / (1024 * 1024)
                  : 0,
              min: 0,
              max: 1000,
              divisions: 20,
              label: offlineSettings.maxDailyBandwidthUsage > 0
                  ? '${(offlineSettings.maxDailyBandwidthUsage / (1024 * 1024)).round()} MB'
                  : 'Unlimited',
              onChanged: (value) {
                final bytes = value > 0 ? (value * 1024 * 1024).round() : 0;
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setMaxDailyBandwidthUsage(bytes);
              },
            ),
          ],
        ),

        SizedBox(height: 16),

        // Display settings
        _buildSettingsSection(
          title: 'Display Settings',
          icon: Icons.visibility,
          children: [
            // Show offline content indicators
            _buildSwitchSetting(
              title: 'Show Offline Content Indicators',
              subtitle: 'Show indicators for offline translations',
              value: offlineSettings.showOfflineContentIndicators,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setShowOfflineContentIndicators(value);
              },
            ),

            // Show sync status notifications
            _buildSwitchSetting(
              title: 'Show Sync Status Notifications',
              subtitle: 'Show notifications for language pack sync status',
              value: offlineSettings.showSyncStatusNotifications,
              onChanged: (value) {
                ref
                    .read(offlineSettingsProvider.notifier)
                    .setShowSyncStatusNotifications(value);
              },
            ),
          ],
        ),
      ],
    );
  }

  /// Build a settings section
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// Build a switch setting
  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppTheme.textPrimaryColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: AppTheme.textSecondaryColor,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build a slider setting
  Widget _buildSliderSetting({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String label,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          title: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          contentPadding: EdgeInsets.zero,
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          label: label,
          onChanged: onChanged,
          activeColor: AppTheme.primaryColor,
        ),
      ],
    );
  }

  /// Clean up unused language packs
  Future<void> _cleanupUnusedLanguagePacks() async {
    try {
      final offlineTranslationManager =
          ref.read(offlineTranslationManagerProvider);
      await offlineTranslationManager.cleanupUnusedLanguagePacks();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unused language packs cleaned up successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cleaning up language packs: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteLanguagePack(LanguagePackModel pack) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Language Pack'),
        content: Text(
          'Are you sure you want to delete the ${pack.languageName} language pack? '
          'You will need to download it again to use offline translation for this language.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref
            .read(languagePackServiceProvider)
            .deleteLanguagePack(pack.languageCode);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Language pack deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting language pack: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
