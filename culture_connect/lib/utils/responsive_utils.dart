import 'package:flutter/material.dart';

/// Responsive utility class to replace flutter_screenutil functionality
/// This provides consistent sizing across different screen sizes
class ResponsiveUtils {
  static const double _designWidth = 375.0;
  static const double _designHeight = 812.0;

  /// Get screen width
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Get screen height
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Get responsive width based on design width
  static double w(BuildContext context, double width) {
    return (width / _designWidth) * screenWidth(context);
  }

  /// Get responsive height based on design height
  static double h(BuildContext context, double height) {
    return (height / _designHeight) * screenHeight(context);
  }

  /// Get responsive font size
  static double sp(BuildContext context, double fontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _designWidth;
    return fontSize * scaleFactor;
  }

  /// Get responsive radius
  static double r(BuildContext context, double radius) {
    return w(context, radius);
  }

  /// Get responsive padding
  static EdgeInsets padding(
    BuildContext context, {
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    if (all != null) {
      return EdgeInsets.all(w(context, all));
    }

    return EdgeInsets.only(
      left: left != null
          ? w(context, left)
          : (horizontal != null ? w(context, horizontal) : 0),
      top: top != null
          ? h(context, top)
          : (vertical != null ? h(context, vertical) : 0),
      right: right != null
          ? w(context, right)
          : (horizontal != null ? w(context, horizontal) : 0),
      bottom: bottom != null
          ? h(context, bottom)
          : (vertical != null ? h(context, vertical) : 0),
    );
  }

  /// Get responsive margin
  static EdgeInsets margin(
    BuildContext context, {
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return padding(
      context,
      all: all,
      horizontal: horizontal,
      vertical: vertical,
      left: left,
      top: top,
      right: right,
      bottom: bottom,
    );
  }

  /// Check if screen is small (phone)
  static bool isSmallScreen(BuildContext context) {
    return screenWidth(context) < 600;
  }

  /// Check if screen is medium (tablet)
  static bool isMediumScreen(BuildContext context) {
    final width = screenWidth(context);
    return width >= 600 && width < 1200;
  }

  /// Check if screen is large (desktop)
  static bool isLargeScreen(BuildContext context) {
    return screenWidth(context) >= 1200;
  }

  /// Get responsive text style
  static TextStyle textStyle(
    BuildContext context, {
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    return TextStyle(
      fontSize: sp(context, fontSize),
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
    );
  }
}

/// Extension methods for easier usage
extension ResponsiveExtension on num {
  /// Convert to responsive width
  double w(BuildContext context) => ResponsiveUtils.w(context, toDouble());

  /// Convert to responsive height
  double h(BuildContext context) => ResponsiveUtils.h(context, toDouble());

  /// Convert to responsive font size
  double sp(BuildContext context) => ResponsiveUtils.sp(context, toDouble());

  /// Convert to responsive radius
  double r(BuildContext context) => ResponsiveUtils.r(context, toDouble());
}
