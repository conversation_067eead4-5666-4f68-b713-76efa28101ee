import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart' as wm;
import 'package:uuid/uuid.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:disk_space/disk_space.dart';

import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/content_priority.dart';
import 'package:culture_connect/models/offline/content_conflict_resolution.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/models/offline/storage_usage.dart';
import 'package:culture_connect/models/offline/content_conflict.dart';
import 'package:culture_connect/models/offline/sync_schedule.dart';
import 'package:culture_connect/models/offline/device_state.dart';
import 'package:culture_connect/models/offline/offline_settings.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// The task name for background sync
const String backgroundSyncTask = 'com.cultureconnect.backgroundSync';

/// Service for managing enhanced offline mode functionality
class EnhancedOfflineModeService {
  /// The connectivity instance
  final Connectivity _connectivity;

  /// The shared preferences instance
  final SharedPreferences _preferences;

  /// The logging service
  final LoggingService _loggingService;

  /// The error handling service
  final ErrorHandlingService? _errorHandlingService;

  /// The battery instance
  final Battery _battery = Battery();

  /// The device info instance
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// Subscription for connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Subscription for battery changes
  StreamSubscription<BatteryState>? _batterySubscription;

  /// Whether the device is currently online
  bool _isOnline = true;

  /// The current network type
  NetworkType _networkType = NetworkType.unknown;

  /// Whether the device is currently charging
  bool _isCharging = false;

  /// The current battery level
  int _batteryLevel = 100;

  /// Stream controller for connectivity status changes
  final StreamController<bool> _connectivityStreamController =
      StreamController<bool>.broadcast();

  /// Stream controller for sync status changes
  final StreamController<SyncStatusUpdate> _syncStatusStreamController =
      StreamController<SyncStatusUpdate>.broadcast();

  /// Stream controller for device state changes
  final StreamController<DeviceState> _deviceStateStreamController =
      StreamController<DeviceState>.broadcast();

  /// Stream controller for storage usage changes
  final StreamController<StorageUsage> _storageUsageStreamController =
      StreamController<StorageUsage>.broadcast();

  /// Stream controller for bandwidth usage changes
  final StreamController<DailyBandwidthUsage> _bandwidthUsageStreamController =
      StreamController<DailyBandwidthUsage>.broadcast();

  /// Stream controller for conflict detection
  final StreamController<ContentConflict> _conflictStreamController =
      StreamController<ContentConflict>.broadcast();

  /// Map of offline content by ID
  final Map<String, OfflineContent> _offlineContent = {};

  /// Map of content conflicts by ID
  final Map<String, ContentConflict> _contentConflicts = {};

  /// List of bandwidth usage records
  final List<BandwidthUsage> _bandwidthUsage = [];

  /// List of sync schedules
  final List<SyncSchedule> _syncSchedules = [];

  /// The current offline settings
  late OfflineSettings _offlineSettings;

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Directory for storing offline content
  late Directory _offlineContentDirectory;

  /// Creates a new enhanced offline mode service
  EnhancedOfflineModeService(
    this._connectivity,
    this._preferences,
    this._loggingService,
    this._errorHandlingService,
  );

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize the offline content directory
      final appDir = await getApplicationDocumentsDirectory();
      _offlineContentDirectory = Directory('${appDir.path}/offline_content');

      if (!await _offlineContentDirectory.exists()) {
        await _offlineContentDirectory.create(recursive: true);
      }

      // Load offline settings
      await _loadOfflineSettings();

      // Load offline content metadata
      await _loadOfflineContentMetadata();

      // Load content conflicts
      await _loadContentConflicts();

      // Load bandwidth usage
      await _loadBandwidthUsage();

      // Load sync schedules
      await _loadSyncSchedules();

      // Initialize device state monitoring
      await _initDeviceStateMonitoring();

      // Initialize background sync
      await _initBackgroundSync();

      // Start periodic tasks
      _startPeriodicTasks();

      _isInitialized = true;
      _loggingService.info(
          'EnhancedOfflineModeService', 'Initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
          'EnhancedOfflineModeService', 'Failed to initialize', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService.initialize',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Initialize device state monitoring
  Future<void> _initDeviceStateMonitoring() async {
    // Check initial connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    _isOnline = connectivityResult != ConnectivityResult.none;

    // Determine network type
    if (connectivityResult == ConnectivityResult.wifi) {
      _networkType = NetworkType.wifi;
    } else if (connectivityResult == ConnectivityResult.mobile) {
      _networkType = NetworkType.mobile;
    } else {
      _networkType = NetworkType.unknown;
    }

    // Notify listeners
    _connectivityStreamController.add(_isOnline);

    // Listen for connectivity changes
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      // Update network type
      if (result == ConnectivityResult.wifi) {
        _networkType = NetworkType.wifi;
      } else if (result == ConnectivityResult.mobile) {
        _networkType = NetworkType.mobile;
      } else {
        _networkType = NetworkType.unknown;
      }

      // Notify listeners if status changed
      if (wasOnline != _isOnline) {
        _connectivityStreamController.add(_isOnline);

        // If we just came back online, start sync
        if (!wasOnline && _isOnline) {
          syncOfflineContent();
        }
      }

      // Update device state
      _updateDeviceState();
    });

    // Check initial battery state
    final batteryState = await _battery.batteryState;
    _isCharging = batteryState == BatteryState.charging ||
        batteryState == BatteryState.full;

    // Get initial battery level
    _batteryLevel = await _battery.batteryLevel;

    // Listen for battery state changes
    _batterySubscription = _battery.onBatteryStateChanged.listen((state) {
      _isCharging =
          state == BatteryState.charging || state == BatteryState.full;

      // Update battery level
      _battery.batteryLevel.then((level) {
        _batteryLevel = level;

        // Update device state
        _updateDeviceState();
      });
    });

    // Initial device state update
    await _updateDeviceState();

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Device state monitoring initialized');
  }

  /// Update the device state and notify listeners
  Future<void> _updateDeviceState() async {
    try {
      // Get available storage
      final availableStorage = await _getAvailableStorage();

      // Create device state
      final deviceState = DeviceState(
        isConnected: _isOnline,
        networkType: _networkType,
        isCharging: _isCharging,
        batteryLevel: _batteryLevel,
        isPowerSaveMode: false, // Not available on all platforms
        isIdle: false, // Not available on all platforms
        availableStorage: availableStorage,
      );

      // Notify listeners
      _deviceStateStreamController.add(deviceState);
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to update device state', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._updateDeviceState',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Get the available storage space
  Future<int> _getAvailableStorage() async {
    try {
      final freeSpace = await DiskSpace.getFreeDiskSpace;
      return ((freeSpace ?? 0) * 1024 * 1024).toInt(); // Convert MB to bytes
    } catch (e) {
      _loggingService.error(
          'EnhancedOfflineModeService', 'Failed to get available storage', e);
      return 0;
    }
  }

  /// Start periodic tasks
  void _startPeriodicTasks() {
    // Update storage usage every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _updateStorageUsage();
    });

    // Check for content to sync every minute
    Timer.periodic(const Duration(minutes: 1), (_) {
      _checkScheduledSync();
    });

    // Clean up expired content daily
    Timer.periodic(const Duration(days: 1), (_) {
      if (_offlineSettings.autoCleanupExpiredContent) {
        _cleanupExpiredContent();
      }
    });

    // Update device state every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _updateDeviceState();
    });
  }

  /// Check if any scheduled sync should run now
  Future<void> _checkScheduledSync() async {
    if (!_isOnline) return;

    // Check if any schedule is active now
    for (final schedule in _syncSchedules) {
      if (schedule.isEnabled && schedule.isActiveNow()) {
        // Check device state constraints
        if (schedule.syncOnlyOnWifi && _networkType != NetworkType.wifi) {
          continue;
        }

        if (schedule.syncOnlyWhenCharging && !_isCharging) {
          continue;
        }

        if (schedule.syncOnlyAboveBatteryLevel &&
            _batteryLevel < schedule.batteryLevelThreshold) {
          continue;
        }

        // Sync content for this schedule
        await _syncContentForSchedule(schedule);
      }
    }
  }

  /// Sync content for a specific schedule
  Future<void> _syncContentForSchedule(SyncSchedule schedule) async {
    try {
      _loggingService.debug('EnhancedOfflineModeService',
          'Running scheduled sync: ${schedule.name}');

      // Get content matching the schedule's content types
      final contentToSync = _offlineContent.values
          .where((content) =>
              schedule.contentTypes.contains(content.contentType) &&
              (content.syncStatus == SyncStatus.pending ||
                  content.syncStatus == SyncStatus.failed))
          .toList();

      // Sort by priority
      contentToSync
          .sort((a, b) => b.priority.value.compareTo(a.priority.value));

      // Sync each content item
      for (final content in contentToSync) {
        await _syncContentItem(content);
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Scheduled sync completed: ${schedule.name}');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to run scheduled sync', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._syncContentForSchedule',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Sync a specific content item
  Future<bool> _syncContentItem(OfflineContent content) async {
    try {
      // Update sync status
      final updatingContent = content.copyWith(
        syncStatus: SyncStatus.syncing,
        lastSyncAttempt: DateTime.now(),
      );

      _offlineContent[content.id] = updatingContent;
      await _saveOfflineContentMetadata();

      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: content.id,
        status: SyncStatus.syncing,
        message: 'Syncing...',
      ));

      // This would be implemented by the specific content type service
      // For now, we'll just simulate a successful sync
      await Future.delayed(const Duration(seconds: 2));

      // Track bandwidth usage (simulated)
      final downloadSize =
          content.estimatedBandwidth ?? 1024 * 1024; // 1 MB default
      await _trackBandwidthUsage(
        contentType: content.contentType,
        contentId: content.id,
        bytesDownloaded: downloadSize,
        bytesUploaded: 0,
      );

      // Update sync status
      final syncedContent = updatingContent.copyWith(
        syncStatus: SyncStatus.synced,
        lastSyncTime: DateTime.now(),
        bandwidthUsed: downloadSize,
      );

      _offlineContent[content.id] = syncedContent;
      await _saveOfflineContentMetadata();

      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: content.id,
        status: SyncStatus.synced,
        message: 'Synced successfully',
      ));

      // Update storage usage
      _updateStorageUsage();

      return true;
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to sync content: ${content.id}', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._syncContentItem',
          stackTrace: stackTrace,
        );
      }

      // Update sync status
      final failedContent = content.copyWith(
        syncStatus: SyncStatus.failed,
        lastSyncAttempt: DateTime.now(),
        errorMessage: e.toString(),
      );

      _offlineContent[content.id] = failedContent;
      await _saveOfflineContentMetadata();

      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: content.id,
        status: SyncStatus.failed,
        message: 'Sync failed: ${e.toString()}',
      ));

      return false;
    }
  }

  /// Track bandwidth usage
  Future<void> _trackBandwidthUsage({
    required String contentType,
    required String contentId,
    required int bytesDownloaded,
    required int bytesUploaded,
  }) async {
    try {
      // Create bandwidth usage record
      final usage = BandwidthUsage(
        id: const Uuid().v4(),
        date: DateTime.now(),
        networkType: _networkType,
        contentType: contentType,
        contentId: contentId,
        bytesDownloaded: bytesDownloaded,
        bytesUploaded: bytesUploaded,
      );

      // Add to list
      _bandwidthUsage.add(usage);

      // Save to storage
      await _saveBandwidthUsage();

      // Check if we've exceeded the daily limit
      if (_offlineSettings.maxDailyBandwidthUsage > 0) {
        final today = DateTime.now();
        final todayUsage = _bandwidthUsage.where((usage) {
          return usage.date.year == today.year &&
              usage.date.month == today.month &&
              usage.date.day == today.day;
        }).fold<int>(0, (sum, usage) => sum + usage.totalBytes);

        if (todayUsage > _offlineSettings.maxDailyBandwidthUsage) {
          _loggingService.warning(
              'EnhancedOfflineModeService', 'Daily bandwidth limit exceeded');
          // We could pause syncing here or notify the user
        }
      }

      // Notify listeners of updated daily usage
      final dailyUsage =
          DailyBandwidthUsage.fromUsages(_bandwidthUsage, DateTime.now());
      _bandwidthUsageStreamController.add(dailyUsage);
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to track bandwidth usage', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._trackBandwidthUsage',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Load offline settings from shared preferences
  Future<void> _loadOfflineSettings() async {
    try {
      final settingsJson = _preferences.getString('enhanced_offline_settings');

      if (settingsJson != null) {
        _offlineSettings = OfflineSettings.fromJson(jsonDecode(settingsJson));
      } else {
        _offlineSettings = OfflineSettings.defaultSettings();
        await _saveOfflineSettings();
      }

      _loggingService.debug(
          'EnhancedOfflineModeService', 'Loaded offline settings');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to load offline settings', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._loadOfflineSettings',
          stackTrace: stackTrace,
        );
      }

      // Use default settings if loading fails
      _offlineSettings = OfflineSettings.defaultSettings();
    }
  }

  /// Save offline settings to shared preferences
  Future<void> _saveOfflineSettings() async {
    try {
      await _preferences.setString(
          'enhanced_offline_settings', jsonEncode(_offlineSettings.toJson()));
      _loggingService.debug(
          'EnhancedOfflineModeService', 'Saved offline settings');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to save offline settings', e, stackTrace);
      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: e,
          context: 'EnhancedOfflineModeService._saveOfflineSettings',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Load offline content metadata from shared preferences
  Future<void> _loadOfflineContentMetadata() async {
    try {
      final contentList =
          _preferences.getStringList('enhanced_offline_content_list') ?? [];

      _offlineContent.clear();
      for (final contentJson in contentList) {
        try {
          final content = OfflineContent.fromJson(jsonDecode(contentJson));
          _offlineContent[content.id] = content;
        } catch (e) {
          _loggingService.error('EnhancedOfflineModeService',
              'Failed to parse offline content', e);
        }
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Loaded ${_offlineContent.length} offline content items');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to load offline content metadata', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._loadOfflineContentMetadata',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Save offline content metadata to shared preferences
  Future<void> _saveOfflineContentMetadata() async {
    try {
      final contentList = _offlineContent.values
          .map((content) => jsonEncode(content.toJson()))
          .toList();

      await _preferences.setStringList(
          'enhanced_offline_content_list', contentList);

      _loggingService.debug('EnhancedOfflineModeService',
          'Saved ${contentList.length} offline content items');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to save offline content metadata', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._saveOfflineContentMetadata',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Load content conflicts from shared preferences
  Future<void> _loadContentConflicts() async {
    try {
      final conflictList =
          _preferences.getStringList('content_conflicts') ?? [];

      _contentConflicts.clear();
      for (final conflictJson in conflictList) {
        try {
          final conflict = ContentConflict.fromJson(jsonDecode(conflictJson));
          _contentConflicts[conflict.id] = conflict;
        } catch (e) {
          _loggingService.error('EnhancedOfflineModeService',
              'Failed to parse content conflict', e);
        }
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Loaded ${_contentConflicts.length} content conflicts');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to load content conflicts', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._loadContentConflicts',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Save content conflicts to shared preferences
  Future<void> _saveContentConflicts() async {
    try {
      final conflictList = _contentConflicts.values
          .map((conflict) => jsonEncode(conflict.toJson()))
          .toList();

      await _preferences.setStringList('content_conflicts', conflictList);

      _loggingService.debug('EnhancedOfflineModeService',
          'Saved ${conflictList.length} content conflicts');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to save content conflicts', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._saveContentConflicts',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Load bandwidth usage from shared preferences
  Future<void> _loadBandwidthUsage() async {
    try {
      final usageList = _preferences.getStringList('bandwidth_usage') ?? [];

      _bandwidthUsage.clear();
      for (final usageJson in usageList) {
        try {
          final usage = BandwidthUsage.fromJson(jsonDecode(usageJson));
          _bandwidthUsage.add(usage);
        } catch (e) {
          _loggingService.error('EnhancedOfflineModeService',
              'Failed to parse bandwidth usage', e);
        }
      }

      // Limit the history to the last 30 days
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      _bandwidthUsage
          emoveWhere((usage) => usage.date.isBefore(thirtyDaysAgo));

      _loggingService.debug('EnhancedOfflineModeService',
          'Loaded ${_bandwidthUsage.length} bandwidth usage records');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to load bandwidth usage', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._loadBandwidthUsage',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Save bandwidth usage to shared preferences
  Future<void> _saveBandwidthUsage() async {
    try {
      // Limit the history to the last 30 days
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      _bandwidthUsage
          emoveWhere((usage) => usage.date.isBefore(thirtyDaysAgo));

      final usageList =
          _bandwidthUsage.map((usage) => jsonEncode(usage.toJson())).toList();

      await _preferences.setStringList('bandwidth_usage', usageList);

      _loggingService.debug('EnhancedOfflineModeService',
          'Saved ${usageList.length} bandwidth usage records');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to save bandwidth usage', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._saveBandwidthUsage',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Load sync schedules from shared preferences
  Future<void> _loadSyncSchedules() async {
    try {
      final scheduleList = _preferences.getStringList('sync_schedules') ?? [];

      _syncSchedules.clear();
      for (final scheduleJson in scheduleList) {
        try {
          final schedule = SyncSchedule.fromJson(jsonDecode(scheduleJson));
          _syncSchedules.add(schedule);
        } catch (e) {
          _loggingService.error(
              'EnhancedOfflineModeService', 'Failed to parse sync schedule', e);
        }
      }

      // If no schedules exist, create a default one
      if (_syncSchedules.isEmpty) {
        _syncSchedules.add(SyncSchedule(
          id: const Uuid().v4(),
          name: 'Default Schedule',
          isEnabled: true,
          daysOfWeek: DayOfWeek.values.toList(),
          timeRanges: [
            const TimeOfDayRange(
              startTime: TimeOfDay(hour: 0, minute: 0),
              endTime: TimeOfDay(hour: 23, minute: 59),
            ),
          ],
          syncOnlyOnWifi: true,
          syncOnlyWhenCharging: false,
          syncOnlyAboveBatteryLevel: true,
          batteryLevelThreshold: 20,
          contentTypes: [
            'experience',
            'itinerary',
            'hotel',
            'flight',
            'ar_content',
            'timeline'
          ],
        ));

        await _saveSyncSchedules();
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Loaded ${_syncSchedules.length} sync schedules');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to load sync schedules', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._loadSyncSchedules',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Save sync schedules to shared preferences
  Future<void> _saveSyncSchedules() async {
    try {
      final scheduleList = _syncSchedules
          .map((schedule) => jsonEncode(schedule.toJson()))
          .toList();

      await _preferences.setStringList('sync_schedules', scheduleList);

      _loggingService.debug('EnhancedOfflineModeService',
          'Saved ${scheduleList.length} sync schedules');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to save sync schedules', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._saveSyncSchedules',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Initialize background sync
  Future<void> _initBackgroundSync() async {
    try {
      // Initialize Workmanager
      await wm.Workmanager().initialize(
        _callbackDispatcher,
        isInDebugMode: kDebugMode,
      );

      // Schedule background sync based on settings
      await _scheduleBackgroundSync();

      _loggingService.debug(
          'EnhancedOfflineModeService', 'Background sync initialized');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to initialize background sync', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._initBackgroundSync',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Schedule background sync based on settings
  Future<void> _scheduleBackgroundSync() async {
    try {
      // Cancel existing tasks
      await wm.Workmanager().cancelByTag(backgroundSyncTask);

      // Only schedule if background sync is enabled
      if (!_offlineSettings.enableBackgroundSync) {
        _loggingService.debug(
            'EnhancedOfflineModeService', 'Background sync is disabled');
        return;
      }

      // Calculate frequency in minutes
      final frequency =
          Duration(minutes: _offlineSettings.backgroundSyncFrequency);

      // Schedule the task
      await wm.Workmanager()egisterPeriodicTask(
        backgroundSyncTask,
        backgroundSyncTask,
        frequency: frequency,
        constraints: wm.Constraints(
          networkType: _offlineSettings.syncOnlyOnWifi
              ? wm.NetworkType.connected
              : wm.NetworkType.not_required,
          requiresBatteryNotLow: true,
          requiresCharging: _offlineSettings.syncOnlyWhenCharging,
        ),
        existingWorkPolicy: wm.ExistingWorkPolicyeplace,
        backoffPolicy: wm.BackoffPolicy.exponential,
      );

      _loggingService.debug('EnhancedOfflineModeService',
          'Background sync scheduled with frequency: ${frequency.inMinutes} minutes');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to schedule background sync', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._scheduleBackgroundSync',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Callback dispatcher for background sync
  @pragma('vm:entry-point')
  static void _callbackDispatcher() {
    wm.Workmanager().executeTask((task, inputData) async {
      try {
        if (task == backgroundSyncTask) {
          // This would be implemented to perform the actual sync
          // For now, we'll just return success
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    });
  }

  /// Sync all offline content
  Future<void> syncOfflineContent() async {
    if (!_isOnline) {
      _loggingService.debug('EnhancedOfflineModeService',
          'Cannot sync offline content: device is offline');
      return;
    }

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Starting offline content sync');

    // Get all pending content
    final pendingContent = _offlineContent.values
        here((content) =>
            content.syncStatus == SyncStatus.pending ||
            content.syncStatus == SyncStatus.failed)
        .toList();

    // Sort by priority
    pendingContent.sort((a, b) => b.priority.value.compareTo(a.priority.value));

    // Check if we have enough storage
    final availableStorage = await _getAvailableStorage();
    if (availableStorage < _offlineSettings.storageThresholdForCleanup) {
      _loggingServicearning('EnhancedOfflineModeService',
          'Low storage space, cleaning up before sync');
      await _cleanupLowPriorityContent();
    }

    // Sync each content item
    for (final content in pendingContent) {
      await _syncContentItem(content);
    }

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Offline content sync completed');
  }

  /// Update storage usage
  Future<void> _updateStorageUsage() async {
    try {
      // Get total storage space
      final totalSpace = await DiskSpace.getTotalDiskSpace;
      final totalSpaceBytes =
          ((totalSpace ?? 0) * 1024 * 1024).toInt(); // Convert MB to bytes

      // Get used space by content type
      final usageByContentType = <String, int>{};
      int totalUsedSpace = 0;

      // Calculate usage by content type
      for (final contentType in _getUniqueContentTypes()) {
        final contentItems = getOfflineContentByType(contentType);
        int typeUsage = 0;

        for (final content in contentItems) {
          if (content.contentSize != null) {
            typeUsage += content.contentSize!;
          }
        }

        usageByContentType[contentType] = typeUsage;
        totalUsedSpace += typeUsage;
      }

      // Create storage usage object
      final storageUsage = StorageUsage(
        totalSpace: totalSpaceBytes,
        usedSpace: totalUsedSpace,
        usageByContentType: usageByContentType,
      );

      // Notify listeners
      _storageUsageStreamController.add(storageUsage);

      // Check if we need to clean up
      if (_offlineSettings.autoCleanupWhenStorageLow &&
          totalUsedSpace > _offlineSettings.maxStorageSpace) {
        _loggingServicearning('EnhancedOfflineModeService',
            'Storage limit exceeded, cleaning up');
        await _cleanupLowPriorityContent();
      }
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to update storage usage', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._updateStorageUsage',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Get unique content types
  List<String> _getUniqueContentTypes() {
    return _offlineContent.values
        .map((content) => content.contentType)
        .toSet()
        .toList();
  }

  /// Clean up expired content
  Future<void> _cleanupExpiredContent() async {
    try {
      final expiredContent = _offlineContent.values
          here((content) =>
              content.expirationTime != null &&
              content.expirationTime!.isBefore(DateTime.now()))
          .toList();

      _loggingService.debug('EnhancedOfflineModeService',
          'Cleaning up ${expiredContent.length} expired content items');

      for (final content in expiredContent) {
        await removeOfflineContent(content.id);
      }

      // Update storage usage
      await _updateStorageUsage();
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to clean up expired content', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._cleanupExpiredContent',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Clean up low priority content
  Future<void> _cleanupLowPriorityContent() async {
    try {
      // Get all content sorted by priority (lowest first)
      final allContent = _offlineContent.values.toList()
        ..sort((a, b) => a.priority.value.compareTo(b.priority.value));

      // Calculate how much space we need to free up
      final currentUsage = allContent
          here((content) => content.contentSize != null)
          .fold<int>(0, (sum, content) => sum + (content.contentSize ?? 0));

      final targetUsage =
          (_offlineSettings.maxStorageSpace * 0.8).toInt(); // Target 80% of max
      int spaceToFree = currentUsage - targetUsage;

      if (spaceToFree <= 0) {
        _loggingService.debug('EnhancedOfflineModeService',
            'No need to clean up low priority content');
        return;
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Need to free up ${_formatBytes(spaceToFree)} of space');

      // Remove low priority content until we've freed up enough space
      int freedSpace = 0;
      for (final content in allContent) {
        // Skip critical content
        if (content.priority == ContentPriority.critical) continue;

        // Skip content that is being edited offline
        if (content.isBeingEditedOffline) continue;

        // Skip content that is a dependency of other content
        if (content.isDependencyOf(allContent)) continue;

        // Remove content
        await removeOfflineContent(content.id);

        // Update freed space
        freedSpace += content.contentSize ?? 0;

        // Check if we've freed up enough space
        if (freedSpace >= spaceToFree) break;
      }

      _loggingService.debug('EnhancedOfflineModeService',
          'Freed up ${_formatBytes(freedSpace)} of space');

      // Update storage usage
      await _updateStorageUsage();
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to clean up low priority content', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._cleanupLowPriorityContent',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Detect and handle conflicts
  Future<void> _detectAndHandleConflicts(
      OfflineContent localContent, Map<String, dynamic> serverData) async {
    try {
      // Extract server version and modified time
      final serverVersion = serverData['version'] ?? 1;
      final serverModifiedTime = serverData['lastModifiedTime'] != null
          ? DateTime.parse(serverData['lastModifiedTime'])
          : DateTime.now();

      // Check if there's a conflict
      if (localContent.version != serverVersion) {
        _loggingService.debug('EnhancedOfflineModeService',
            'Detected conflict for content: ${localContent.id}');

        // Create conflict object
        final conflict = ContentConflict(
          id: const Uuid().v4(),
          contentId: localContent.id,
          contentType: localContent.contentType,
          contentTitle: localContent.title,
          serverVersion: serverVersion,
          clientVersion: localContent.version,
          serverModifiedTime: serverModifiedTime,
          clientModifiedTime: localContent.lastModifiedTime ?? DateTime.now(),
          status: ContentConflictStatus.pending,
          serverData: serverData,
          clientData: localContent.toJson(),
        );

        // Add to conflicts map
        _contentConflicts[conflict.id] = conflict;

        // Save conflicts
        await _saveContentConflicts();

        // Notify listeners
        _conflictStreamController.add(conflict);

        // Handle conflict based on resolution strategy
        await _resolveConflict(conflict);
      }
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to detect and handle conflicts', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._detectAndHandleConflicts',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Resolve a conflict
  Future<void> _resolveConflict(ContentConflict conflict) async {
    try {
      // Get the content
      final content = _offlineContent[conflict.contentId];
      if (content == null) {
        _loggingServicearning('EnhancedOfflineModeService',
            'Cannot resolve conflict: content not found');
        return;
      }

      // Get the resolution strategy
      final resolution = content.conflictResolution;

      // Resolve based on strategy
      ContentConflict resolvedConflict;

      switch (resolution) {
        case ContentConflictResolution.serverWins:
          // Use server version
          final serverContent = OfflineContent.fromJson(conflict.serverData);
          _offlineContent[content.id] = serverContent;
          resolvedConflict = conflictesolveWithServerVersion();
          break;

        case ContentConflictResolution.clientWins:
          // Keep client version, but update version number
          final updatedContent = content.copyWith(
            version: conflict.serverVersion + 1,
          );
          _offlineContent[content.id] = updatedContent;
          resolvedConflict = conflictesolveWithClientVersion();
          break;

        case ContentConflictResolution.merge:
          // Try to merge changes
          final mergedData =
              await _mergeChanges(conflict.clientData, conflict.serverData);
          final mergedContent = OfflineContent.fromJson(mergedData);
          _offlineContent[content.id] = mergedContent;
          resolvedConflict = conflictesolveWithMergedVersion(mergedData);
          break;

        case ContentConflictResolution.askUser:
          // Defer resolution until user decides
          resolvedConflict = conflict.defer();
          break;
      }

      // Update conflict
      _contentConflicts[resolvedConflict.id] = resolvedConflict;

      // Save conflicts
      await _saveContentConflicts();

      // Save content metadata
      await _saveOfflineContentMetadata();

      // Notify listeners
      _conflictStreamController.add(resolvedConflict);

      _loggingService.debug('EnhancedOfflineModeService',
          'Resolved conflict for content: ${content.id} with strategy: ${resolution.name}');
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to resolve conflict', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeService._resolveConflict',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Merge changes between client and server versions
  Future<Map<String, dynamic>> _mergeChanges(
      Map<String, dynamic> clientData, Map<String, dynamic> serverData) async {
    // This is a simple merge strategy that takes the newer values for each field
    // A more sophisticated merge strategy would be needed for complex data structures

    // Start with server data as base
    final mergedData = Map<String, dynamic>.from(serverData);

    // Get timestamps
    final clientModifiedTime = clientData['lastModifiedTime'] != null
        ? DateTime.parse(clientData['lastModifiedTime'])
        : DateTime.now();
    final serverModifiedTime = serverData['lastModifiedTime'] != null
        ? DateTime.parse(serverData['lastModifiedTime'])
        : DateTime.now();

    // For each field in client data, use client value if it's newer
    for (final key in clientData.keys) {
      // Skip metadata fields
      if (['id', 'version', 'syncStatus', 'lastSyncTime', 'lastSyncAttempt']
          .contains(key)) {
        continue;
      }

      // Use client value if it's newer
      if (clientModifiedTime.isAfter(serverModifiedTime)) {
        mergedData[key] = clientData[key];
      }
    }

    // Update version and modified time
    final clientVersion = clientData['version'] as int? ?? 1;
    final serverVersion = serverData['version'] as int? ?? 1;
    mergedData['version'] = max<int>(clientVersion, serverVersion) + 1;
    mergedData['lastModifiedTime'] = DateTime.now().toIso8601String();

    return mergedData;
  }

  /// Get all offline content
  List<OfflineContent> getAllOfflineContent() {
    return _offlineContent.values.toList();
  }

  /// Get offline content by type
  List<OfflineContent> getOfflineContentByType(String contentType) {
    return _offlineContent.values
        here((content) => content.contentType == contentType)
        .toList();
  }

  /// Check if content is available offline
  bool isContentAvailableOffline(String contentId) {
    return _offlineContent.containsKey(contentId) &&
        _offlineContent[contentId]!.syncStatus == SyncStatus.synced;
  }

  /// Get the sync status for content
  SyncStatus getContentSyncStatus(String contentId) {
    if (!_offlineContent.containsKey(contentId)) {
      return SyncStatus.notSynced;
    }

    return _offlineContent[contentId]!.syncStatus;
  }

  /// Remove content from offline storage
  Future<bool> removeOfflineContent(String contentId) async {
    try {
      if (!_offlineContent.containsKey(contentId)) {
        return false;
      }

      // Get content
      final content = _offlineContent[contentId]!;

      // Remove content file if it exists
      final contentFile =
          File('${_offlineContentDirectory.path}/${content.id}');
      if (await contentFile.exists()) {
        await contentFile.delete();
      }

      // Remove from offline content map
      _offlineContentemove(contentId);

      // Save metadata
      await _saveOfflineContentMetadata();

      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: contentId,
        status: SyncStatus.notSynced,
        message: 'Removed from offline storage',
      ));

      // Update storage usage
      await _updateStorageUsage();

      return true;
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to remove offline content', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context: 'EnhancedOfflineModeServiceemoveOfflineContent',
          stackTrace: stackTrace,
        );
      }

      return false;
    }
  }

  /// Get all pending conflicts
  List<ContentConflict> getPendingConflicts() {
    return _contentConflicts.values
        here((conflict) => conflict.isPending)
        .toList();
  }

  /// Resolve a conflict with the server version
  Future<bool> resolveConflictWithServerVersion(String conflictId) async {
    try {
      final conflict = _contentConflicts[conflictId];
      if (conflict == null || !conflict.isPending) {
        return false;
      }

      // Use server version
      final serverContent = OfflineContent.fromJson(conflict.serverData);
      _offlineContent[conflict.contentId] = serverContent;

      // Update conflict
      final resolvedConflict = conflictesolveWithServerVersion();
      _contentConflicts[conflictId] = resolvedConflict;

      // Save changes
      await _saveContentConflicts();
      await _saveOfflineContentMetadata();

      // Notify listeners
      _conflictStreamController.add(resolvedConflict);

      return true;
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to resolve conflict with server version', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context:
              'EnhancedOfflineModeServiceesolveConflictWithServerVersion',
          stackTrace: stackTrace,
        );
      }

      return false;
    }
  }

  /// Resolve a conflict with the client version
  Future<bool> resolveConflictWithClientVersion(String conflictId) async {
    try {
      final conflict = _contentConflicts[conflictId];
      if (conflict == null || !conflict.isPending) {
        return false;
      }

      // Get the content
      final content = _offlineContent[conflict.contentId];
      if (content == null) {
        return false;
      }

      // Update version
      final updatedContent = content.copyWith(
        version: conflict.serverVersion + 1,
      );
      _offlineContent[content.id] = updatedContent;

      // Update conflict
      final resolvedConflict = conflictesolveWithClientVersion();
      _contentConflicts[conflictId] = resolvedConflict;

      // Save changes
      await _saveContentConflicts();
      await _saveOfflineContentMetadata();

      // Notify listeners
      _conflictStreamController.add(resolvedConflict);

      return true;
    } catch (e, stackTrace) {
      _loggingService.error('EnhancedOfflineModeService',
          'Failed to resolve conflict with client version', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!andleError(
          error: e,
          context:
              'EnhancedOfflineModeServiceesolveConflictWithClientVersion',
          stackTrace: stackTrace,
        );
      }

      return false;
    }
  }

  /// Get the current device state
  Future<DeviceState> getCurrentDeviceState() async {
    await _updateDeviceState();

    return DeviceState(
      isConnected: _isOnline,
      networkType: _networkType,
      isCharging: _isCharging,
      batteryLevel: _batteryLevel,
      isPowerSaveMode: false,
      isIdle: false,
      availableStorage: await _getAvailableStorage(),
    );
  }

  /// Get the current storage usage
  Future<StorageUsage> getCurrentStorageUsage() async {
    await _updateStorageUsage();

    // Get total storage space
    final totalSpace = await DiskSpace.getTotalDiskSpace;
    final totalSpaceBytes =
        ((totalSpace ?? 0) * 1024 * 1024).toInt(); // Convert MB to bytes

    // Get used space by content type
    final usageByContentType = <String, int>{};
    int totalUsedSpace = 0;

    // Calculate usage by content type
    for (final contentType in _getUniqueContentTypes()) {
      final contentItems = getOfflineContentByType(contentType);
      int typeUsage = 0;

      for (final content in contentItems) {
        if (content.contentSize != null) {
          typeUsage += content.contentSize!;
        }
      }

      usageByContentType[contentType] = typeUsage;
      totalUsedSpace += typeUsage;
    }

    return StorageUsage(
      totalSpace: totalSpaceBytes,
      usedSpace: totalUsedSpace,
      usageByContentType: usageByContentType,
    );
  }

  /// Get the current bandwidth usage
  DailyBandwidthUsage getCurrentBandwidthUsage() {
    return DailyBandwidthUsage.fromUsages(_bandwidthUsage, DateTime.now());
  }

  /// Get the bandwidth usage for a specific date
  DailyBandwidthUsage getBandwidthUsageForDate(DateTime date) {
    return DailyBandwidthUsage.fromUsages(_bandwidthUsage, date);
  }

  /// Get the bandwidth usage for a date range
  List<DailyBandwidthUsage> getBandwidthUsageForDateRange(
      DateTime startDate, DateTime endDate) {
    final result = <DailyBandwidthUsage>[];

    // Create a daily usage for each day in the range
    for (var date = startDate;
        date.isBefore(endDate.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      result.add(DailyBandwidthUsage.fromUsages(_bandwidthUsage, date));
    }

    return result;
  }

  /// Update offline settings
  Future<void> updateOfflineSettings(OfflineSettings settings) async {
    _offlineSettings = settings;
    await _saveOfflineSettings();

    // Update background sync if needed
    if (_offlineSettings.enableBackgroundSync) {
      await _scheduleBackgroundSync();
    } else {
      await wm.Workmanager().cancelByTag(backgroundSyncTask);
    }

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Updated offline settings');
  }

  /// Get the current offline settings
  OfflineSettings getOfflineSettings() {
    return _offlineSettings;
  }

  /// Add a sync schedule
  Future<void> addSyncSchedule(SyncSchedule schedule) async {
    _syncSchedules.add(schedule);
    await _saveSyncSchedules();

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Added sync schedule: ${schedule.name}');
  }

  /// Update a sync schedule
  Future<void> updateSyncSchedule(SyncSchedule schedule) async {
    final index = _syncSchedules.indexWhere((s) => s.id == schedule.id);
    if (index >= 0) {
      _syncSchedules[index] = schedule;
      await _saveSyncSchedules();

      _loggingService.debug('EnhancedOfflineModeService',
          'Updated sync schedule: ${schedule.name}');
    }
  }

  /// Remove a sync schedule
  Future<void> removeSyncSchedule(String scheduleId) async {
    _syncSchedulesemoveWhere((s) => s.id == scheduleId);
    await _saveSyncSchedules();

    _loggingService.debug(
        'EnhancedOfflineModeService', 'Removed sync schedule: $scheduleId');
  }

  /// Get all sync schedules
  List<SyncSchedule> getAllSyncSchedules() {
    return _syncSchedules;
  }

  /// Dispose the service
  void dispose() {
    _connectivitySubscription?.cancel();
    _batterySubscription?.cancel();
    _connectivityStreamController.close();
    _syncStatusStreamController.close();
    _deviceStateStreamController.close();
    _storageUsageStreamController.close();
    _bandwidthUsageStreamController.close();
    _conflictStreamController.close();

    _loggingService.debug('EnhancedOfflineModeService', 'Disposed');
  }
}
