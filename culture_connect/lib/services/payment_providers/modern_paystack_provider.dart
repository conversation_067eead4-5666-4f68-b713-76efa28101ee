import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/screens/payment/paystack_webview_screen.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Modern Paystack payment provider using direct API integration
///
/// This implementation replaces the outdated flutter_paystack SDK with
/// a secure, direct API integration approach for enterprise-grade security
class ModernPaystackProvider {
  static const String _baseUrl = 'https://api.paystack.co';
  final LoggingService _loggingService;
  String? _publicKey;
  bool _isInitialized = false;

  ModernPaystackProvider({
    required LoggingService loggingService,
  }) : _loggingService = loggingService;

  /// Initialize Paystack provider with public key
  ///
  /// TODO: Backend Integration Required
  /// - Get public key from backend configuration
  /// - Environment-specific keys (test/live)
  /// - Support for multiple African countries
  Future<void> initialize({
    required String publicKey,
  }) async {
    try {
      _publicKey = publicKey;
      _isInitialized = true;

      _loggingService.info(
        'ModernPaystackProvider',
        'Paystack provider initialized successfully',
        {'environment': publicKey.startsWith('pk_test_') ? 'test' : 'live'},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'ModernPaystackProvider',
        'Failed to initialize Paystack provider',
        {'error': e.toString()},
        stackTrace,
      );
      throw const PaymentProviderException(
        'Failed to initialize Paystack provider',
        provider: 'paystack',
        code: 'PAYSTACK_INIT_FAILED',
      );
    }
  }

  /// Initialize payment transaction
  ///
  /// This method creates a transaction on Paystack's backend and returns
  /// an authorization URL for secure payment processing
  Future<PaystackInitResponse> initializeTransaction({
    required String email,
    required int amount, // Amount in kobo (smallest currency unit)
    required String reference,
    String? currency = 'NGN',
    Map<String, dynamic>? metadata,
    List<String>? channels,
  }) async {
    if (!_isInitialized || _publicKey == null) {
      throw const PaymentProviderException(
        'Paystack provider not initialized',
        provider: 'paystack',
        code: 'PAYSTACK_NOT_INITIALIZED',
      );
    }

    try {
      final requestBody = {
        'email': email,
        'amount': amount,
        'reference': reference,
        'currency': currency,
        if (metadata != null) 'metadata': metadata,
        if (channels != null) 'channels': channels,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/transaction/initialize'),
        headers: {
          'Authorization': 'Bearer $_publicKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && responseData['status'] == true) {
        return PaystackInitResponse.fromJson(responseData['data']);
      } else {
        throw PaymentProviderException(
          responseData['message'] ?? 'Transaction initialization failed',
          provider: 'paystack',
          code: 'PAYSTACK_INIT_FAILED',
          providerCode: responseData['code']?.toString(),
        );
      }
    } catch (e) {
      _loggingService.error(
        'ModernPaystackProvider',
        'Transaction initialization failed',
        {'error': e.toString(), 'reference': reference},
      );
      rethrow;
    }
  }

  /// Verify payment transaction
  ///
  /// This method verifies the payment status with Paystack's backend
  Future<PaystackVerificationResponse> verifyTransaction({
    required String reference,
  }) async {
    if (!_isInitialized || _publicKey == null) {
      throw const PaymentProviderException(
        'Paystack provider not initialized',
        provider: 'paystack',
        code: 'PAYSTACK_NOT_INITIALIZED',
      );
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/transaction/verify/$reference'),
        headers: {
          'Authorization': 'Bearer $_publicKey',
          'Content-Type': 'application/json',
        },
      );

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && responseData['status'] == true) {
        return PaystackVerificationResponse.fromJson(responseData['data']);
      } else {
        throw PaymentProviderException(
          responseData['message'] ?? 'Transaction verification failed',
          provider: 'paystack',
          code: 'PAYSTACK_VERIFICATION_FAILED',
          providerCode: responseData['code']?.toString(),
        );
      }
    } catch (e) {
      _loggingService.error(
        'ModernPaystackProvider',
        'Transaction verification failed',
        {'error': e.toString(), 'reference': reference},
      );
      rethrow;
    }
  }

  /// Process card payment using Paystack Popup
  ///
  /// This method opens Paystack's secure payment popup for card processing
  Future<ModernPaystackResult> processCardPayment({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    if (!context.mounted) {
      throw const PaymentProviderException(
        'Payment context is no longer valid',
        provider: 'paystack',
        code: 'PAYSTACK_CONTEXT_INVALID',
      );
    }

    try {
      // Convert amount to kobo (smallest currency unit)
      final amountInKobo = (amount * 100).toInt();

      // Initialize transaction
      final initResponse = await initializeTransaction(
        email: userEmail,
        amount: amountInKobo,
        reference: transactionReference,
        currency: currency,
        channels: ['card'], // Restrict to card payments
        metadata: {
          'source': 'CultureConnect Mobile App',
          'platform': 'Flutter',
        },
      );

      // Validate context before launching WebView
      if (!context.mounted) {
        throw const PaymentProviderException(
          'Payment context is no longer valid',
          provider: 'paystack',
          code: 'PAYSTACK_CONTEXT_INVALID',
        );
      }

      // Launch WebView for secure payment processing
      final paymentResult = await _launchPaymentWebView(
        context: context,
        authorizationUrl: initResponse.authorizationUrl,
        transactionReference: transactionReference,
        amount: amount,
        currency: currency,
      );

      return paymentResult;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ModernPaystackProvider',
        'Card payment processing failed',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return ModernPaystackResult(
        success: false,
        transactionReference: transactionReference,
        error: 'Card payment failed: $e',
      );
    }
  }

  /// Get supported payment methods for Paystack
  static List<PaymentMethodType> getSupportedPaymentMethods() {
    return [
      PaymentMethodType.card,
      PaymentMethodType.bankTransfer,
      PaymentMethodType.ussd,
      PaymentMethodType.mobileMoney,
    ];
  }

  /// Check if Paystack is available for the current device
  static bool isAvailable() {
    return true; // Available on all platforms via web integration
  }

  /// Launch WebView for payment processing
  Future<ModernPaystackResult> _launchPaymentWebView({
    required BuildContext context,
    required String authorizationUrl,
    required String transactionReference,
    required double amount,
    required String currency,
  }) async {
    try {
      final completer = Completer<ModernPaystackResult>();

      // Navigate to WebView screen
      await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => PaystackWebViewScreen(
            authorizationUrl: authorizationUrl,
            transactionReference: transactionReference,
            amount: amount,
            currency: currency,
            onSuccess: () {
              if (!completer.isCompleted) {
                completer.complete(ModernPaystackResult(
                  success: true,
                  transactionReference: transactionReference,
                  message: 'Payment completed successfully',
                ));
              }
            },
            onCancel: () {
              if (!completer.isCompleted) {
                completer.complete(ModernPaystackResult(
                  success: false,
                  transactionReference: transactionReference,
                  error: 'Payment cancelled by user',
                ));
              }
            },
            onError: (error) {
              if (!completer.isCompleted) {
                completer.complete(ModernPaystackResult(
                  success: false,
                  transactionReference: transactionReference,
                  error: error,
                ));
              }
            },
          ),
        ),
      );

      // Wait for payment completion or return result
      return await completer.future.timeout(
        const Duration(minutes: 15),
        onTimeout: () => ModernPaystackResult(
          success: false,
          transactionReference: transactionReference,
          error: 'Payment session timed out',
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'ModernPaystackProvider',
        'WebView launch failed',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return ModernPaystackResult(
        success: false,
        transactionReference: transactionReference,
        error: 'Failed to launch payment interface: $e',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _publicKey = null;
  }
}

/// Response model for Paystack transaction initialization
class PaystackInitResponse {
  final String authorizationUrl;
  final String accessCode;
  final String reference;

  const PaystackInitResponse({
    required this.authorizationUrl,
    required this.accessCode,
    required this.reference,
  });

  factory PaystackInitResponse.fromJson(Map<String, dynamic> json) {
    return PaystackInitResponse(
      authorizationUrl: json['authorization_url'] as String,
      accessCode: json['access_code'] as String,
      reference: json['reference'] as String,
    );
  }
}

/// Response model for Paystack transaction verification
class PaystackVerificationResponse {
  final String status;
  final String reference;
  final double amount;
  final String currency;
  final DateTime paidAt;
  final Map<String, dynamic>? metadata;

  const PaystackVerificationResponse({
    required this.status,
    required this.reference,
    required this.amount,
    required this.currency,
    required this.paidAt,
    this.metadata,
  });

  factory PaystackVerificationResponse.fromJson(Map<String, dynamic> json) {
    return PaystackVerificationResponse(
      status: json['status'] as String,
      reference: json['reference'] as String,
      amount: (json['amount'] as num).toDouble() / 100, // Convert from kobo
      currency: json['currency'] as String,
      paidAt: DateTime.parse(json['paid_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Result model for modern Paystack payment operations
class ModernPaystackResult {
  final bool success;
  final String transactionReference;
  final String? authorizationUrl;
  final String? accessCode;
  final String? message;
  final String? error;

  const ModernPaystackResult({
    required this.success,
    required this.transactionReference,
    this.authorizationUrl,
    this.accessCode,
    this.message,
    this.error,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transaction_reference': transactionReference,
      if (authorizationUrl != null) 'authorization_url': authorizationUrl,
      if (accessCode != null) 'access_code': accessCode,
      if (message != null) 'message': message,
      if (error != null) 'error': error,
    };
  }
}
