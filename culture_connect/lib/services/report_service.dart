import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/report_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Service for handling report-related operations
class ReportService {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _userId;
  final Uuid _uuid = const Uuid();

  /// Creates a new report service
  ReportService(this._firestore, this._storage, this._userId);

  /// Get all report reasons for a specific type
  Future<List<ReportReason>> getReportReasons(ReportType type) async {
    try {
      final snapshot = await _firestore
          .collection('report_reasons')
          .where('type', isEqualTo: type.toString().split('.').last)
          .get();

      return snapshot.docs
          .map((doc) => ReportReason.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Submit a report
  Future<Report> submitReport({
    required String reportedEntityId,
    required ReportType type,
    required String reason,
    required String description,
    required ReportSeverity severity,
    List<File>? evidence,
  }) async {
    try {
      // Upload evidence if provided
      List<String>? evidenceUrls;
      if (evidence != null && evidence.isNotEmpty) {
        evidenceUrls = await _uploadEvidence(evidence);
      }

      // Create report
      final docRef = _firestore.collection('reports').doc();
      final now = DateTime.now();

      final report = Report(
        id: docRef.id,
        reporterId: _userId,
        reportedEntityId: reportedEntityId,
        type: type,
        status: ReportStatus.pending,
        severity: severity,
        reason: reason,
        description: description,
        evidenceUrls: evidenceUrls,
        submittedAt: now,
        updatedAt: now,
      );

      await docRef.set(report.toJson());
      return report;
    } catch (e) {
      rethrow;
    }
  }

  /// Get all reports submitted by the current user
  Future<List<Report>> getUserReports() async {
    try {
      final snapshot = await _firestore
          .collection('reports')
          .where('reporterId', isEqualTo: _userId)
          .orderBy('submittedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Report.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Get a specific report
  Future<Report?> getReport(String reportId) async {
    try {
      final doc = await _firestore.collection('reports').doc(reportId).get();

      if (!doc.exists) return null;

      return Report.fromJson({...doc.data()!, 'id': doc.id});
    } catch (e) {
      rethrow;
    }
  }

  /// Update a report
  Future<void> updateReport(Report report) async {
    try {
      if (report.reporterId != _userId) {
        throw Exception('You can only update your own reports');
      }

      if (report.status != ReportStatus.pending) {
        throw Exception('You can only update pending reports');
      }

      final updatedReport = report.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('reports')
          .doc(report.id)
          .update(updatedReport.toJson());
    } catch (e) {
      rethrow;
    }
  }

  /// Cancel a report
  Future<void> cancelReport(String reportId) async {
    try {
      final report = await getReport(reportId);
      if (report == null) {
        throw Exception('Report not found');
      }

      if (report.reporterId != _userId) {
        throw Exception('You can only cancel your own reports');
      }

      if (report.status != ReportStatus.pending) {
        throw Exception('You can only cancel pending reports');
      }

      // Delete the report
      await _firestore.collection('reports').doc(reportId).delete();

      // Delete uploaded evidence
      if (report.evidenceUrls != null) {
        for (final url in report.evidenceUrls!) {
          try {
            final ref = _storage.refFromURL(url);
            await ref.delete();
          } catch (e) {
            // Ignore errors when deleting evidence
            print('Error deleting evidence: $e');
          }
        }
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Upload evidence to storage
  Future<List<String>> _uploadEvidence(List<File> evidence) async {
    try {
      final urls = <String>[];

      for (final file in evidence) {
        final fileName =
            '${_userId}_${_uuid.v4()}_${file.path.split('/').last}';
        final ref = _storage.ref().child('report_evidence/$fileName');

        final uploadTask = ref.putFile(file);
        final snapshot = await uploadTask;

        final url = await snapshot.ref.getDownloadURL();
        urls.add(url);
      }

      return urls;
    } catch (e) {
      rethrow;
    }
  }
}

/// Provider for the report service
final reportServiceProvider = Provider<ReportService>((ref) {
  final user = ref.read(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access report services');
  }

  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;

  return ReportService(firestore, storage, user.id);
});

/// Stream provider for user reports
final userReportsProvider = StreamProvider<List<Report>>((ref) {
  final user = ref.read(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access reports');
  }

  return FirebaseFirestore.instance
      .collection('reports')
      .where('reporterId', isEqualTo: user.id)
      .orderBy('submittedAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => Report.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

/// Provider for report reasons
final reportReasonsProvider =
    FutureProvider.family<List<ReportReason>, ReportType>((ref, type) async {
  final firestore = FirebaseFirestore.instance;

  final snapshot = await firestore
      .collection('report_reasons')
      .where('type', isEqualTo: type.toString().split('.').last)
      .get();

  return snapshot.docs
      .map((doc) => ReportReason.fromJson({...doc.data(), 'id': doc.id}))
      .toList();
});
